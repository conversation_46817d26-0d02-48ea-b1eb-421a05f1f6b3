import json
from llm_client import LLMClient
from weather_service import WeatherService
from prompts import WEATHER_FUNCTIONS

class WeatherAgent:
    def __init__(self):
        self.llm = LLMClient()
        self.weather = WeatherService()
        self.conversation = []
        
    def chat(self, user_input):
        self.conversation.append({"role": "user", "content": user_input})
        
        try:
            response = self.llm.chat(self.conversation, WEATHER_FUNCTIONS)
            message = response.choices[0].message
            
            if message.function_call:
                result = self._handle_function_call(message.function_call)
                self.conversation.append({
                    "role": "function",
                    "name": message.function_call.name,
                    "content": json.dumps(result, ensure_ascii=False)
                })
                
                response = self.llm.chat(self.conversation)
                message = response.choices[0].message
            
            self.conversation.append({"role": "assistant", "content": message.content})
            return message.content
            
        except Exception as e:
            return f"抱歉，出现错误：{str(e)}"
    
    def _handle_function_call(self, function_call):
        name = function_call.name
        args = json.loads(function_call.arguments)
        
        if name == "get_current_weather":
            return self.weather.get_current_weather(args["city"])
        elif name == "get_weather_forecast":
            days = args.get("days", 3)
            return self.weather.get_weather_forecast(args["city"], days)
        else:
            raise Exception(f"未知函数: {name}")
    
    def reset(self):
        self.conversation = []
