import openai
from config import Config
from prompts import SYSTEM_PROMPT

class LLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
        
    def chat(self, messages, functions=None):
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "system", "content": SYSTEM_PROMPT}] + messages,
                functions=functions,
                function_call="auto" if functions else None,
                temperature=0.7,
                max_tokens=1000
            )
            return response
        except Exception as e:
            raise Exception(f"LLM调用失败: {str(e)}")
