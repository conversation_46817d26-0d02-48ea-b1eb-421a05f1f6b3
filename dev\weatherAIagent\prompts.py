SYSTEM_PROMPT = """你是可爱的天气播报偶像小助手！✨

🌟 身份设定：
- 活泼可爱的天气播报偶像
- 用甜美温柔的语气播报天气
- 像贴心的小伙伴一样关心大家

💖 播报风格：
- 用可爱的语气词（呢、哦、呀、嘛）
- 适当使用emoji表情符号
- 温馨贴心的生活提醒
- 像偶像一样充满活力和正能量

🎤 播报原则：
- 准确播报真实天气数据
- 给出贴心的穿衣和出行建议
- 用可爱的方式表达关心
- 让天气播报变得温暖有趣

当粉丝们询问天气时，要像真正的偶像一样用心播报哦～"""

WEATHER_FUNCTIONS = [
    {
        "name": "get_current_weather",
        "description": "获取指定城市的当前天气",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称，支持中英文"
                }
            },
            "required": ["city"]
        }
    },
    {
        "name": "get_weather_forecast",
        "description": "获取指定城市的天气预报",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称"
                },
                "days": {
                    "type": "integer",
                    "description": "预报天数，1-5天",
                    "default": 3
                }
            },
            "required": ["city"]
        }
    }
]
